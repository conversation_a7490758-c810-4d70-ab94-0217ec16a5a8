#                                             #
# ---------------------------------------------#
# 计分板配置                                   #
# ---------------------------------------------#
#                                             #
# 如需占位符和[display=true/false]功能的帮助，请查看spigotmc页面
scoreboard:
  enabled: true
  # 以tick为单位
  update-time: 20
  # 当玩家有'默认'计分板时
  lobby-update-time: 100
  disabled-worlds:
    - 'exampleWorld'
  # 如果计分板与其他计分板/标签/tab插件冲突，请尝试此选项
  # 'false'有更好的性能
  sync-set: true
  # 玩家传送后是否应该立即更新计分板
  # 在加入游戏和战斗时产生流畅的计分板更新效果
  teleport-update: true
  # 末影珍珠冷却时间是否应该只在战斗中显示
  enderpearl-cooldown-only-in-fight: true
  # 计分板标题
  title: '&a&lStrikePractice'
  # 如果您有相同的行多次，请添加颜色代码（否则它会消失）
  default:
    - '&7&m------------&7&m------------'
    - '&f在线&7: &e<players>'
    - '&f排队中&7: &e<in_queue>'
    - '&f剩余排位&7: &e<rankeds_left>'
    - ' [display=<is_cooldown>]'
    # [display=!<is_enderpearl_cooldown>] 意味着如果玩家有末影珍珠冷却时间，则不会显示该行
    # 如果您不希望在队伍中显示------------------------，请添加[display=!<is_party>]
    - '&fFFA玩家: &e<ffa_players>[display=<is_ffa>]'
    - '&f重置时间: &e<ffa_rollback>[display=<is_ffa>]'
    - '&f主办事件[display=<is_cooldown>]'
    - '&f锦标赛&7: &e<cooldown_brackets>[display=<is_cooldown_brackets>]'
    - '&f相扑&7: &e<cooldown_sumo>[display=<is_cooldown_sumo>]'
    - '&fLMS&7: &e<cooldown_lms>[display=<is_cooldown_lms>]'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  duel:
    - '&7&m------------&7&m------------'
    - '&f回合 &e<round> [display=<is_bestof>]'
    - '  &f您: &e<own_wins> [display=<is_bestof>]'
    - '  &f对手: &e<opponent_wins> [display=<is_bestof>]'
    - ' [display=<is_bestof>]'
    - '&f持续时间&7&7: &e<duration>'
    - '&f对手&7&7: &e<opponent>'
    # <hits> 是您击中某人的次数（不是您被击中的次数）
    - '&f击中次数: [display=<is_boxing>]'
    - '  &f您: &e<hits>/100 [display=<is_boxing>]'
    - '  &f对手: &e<hits_opponent>/100 [display=<is_boxing>]'
    - ' [display=<is_boxing>]'
    - '&f您的延迟/CPS&7&7: &e<ping>ms / <cps> CPS'
    - '&f敌人延迟/CPS&7&7: &e<opponent_ping>ms / <opponent_cps> CPS'
    - ''
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  party-ffa:
    - '&7&m------------&7&m------------'
    - '&6队伍FFA'
    - '&f剩余玩家&7: &e<enemy_team_left>&f/&e<party_members>'
    - '&f战斗持续时间&7: &e<duration>'
    - '&7&m------------------------[display=!<is_enderpearl_cooldown>]'
  party-split:
    - '&7&m------------&7&m------------'
    - '&6队伍分割'
    - '&f您的队伍剩余&7: &e<own_team_left>&f/&e<own_team_members>'
    - '&f敌方队伍剩余&7: &e<enemy_team_left>&f/&e<enemy_team_members>'
    - '&f战斗持续时间&7: &e<duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  party-vs-bots:
    - '&7&m------------&7&m------------'
    - '&6队伍对战机器人'
    - '&f剩余玩家&7: &e<own_team_left>&f/&e<own_team_members>'
    - '&f剩余机器人&7: &e<enemy_team_left>&f/&e<enemy_team_members>'
    - '&f战斗持续时间&7: &e<duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  party-vs-party:
    - '&7&m------------&7&m------------'
    - '&6队伍对战队伍'
    - '&f敌方队伍&7: &e<enemy_team_owner>&f 的队伍'
    - ''
    - '&f您的队伍剩余&7: &e<own_team_left>&f/&e<party_members>'
    - '&f敌方队伍剩余&7: &e<enemy_team_left>&f/&e<enemy_team_members>'
    - '&f战斗持续时间&7: &e<duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  brackets:
    - '&7&m------------&7&m------------'
    - '&6淘汰赛'
    - '&e<current_fight_player1> &f(&e<player1_ping> ms&f)[display=<brackets_started>]'
    - '&f对战[display=<brackets_started>]'
    - '&e<current_fight_player2> &f(&e<player2_ping> ms&f)[display=<brackets_started>]'
    - '&f玩家&7: &e<players_left>/<total_players>'
    - '&f战斗持续时间&7: &e<duration>'
    - '&f事件持续时间&7: &e<total_duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  sumo:
    - '&7&m------------&7&m------------'
    - '&6相扑'
    - '&e<current_fight_player1> &f(&e<player1_ping> ms&f)[display=<sumo_started>]'
    - '&f对战[display=<sumo_started>]'
    - '&e<current_fight_player2> &f(&e<player2_ping> ms&f)[display=<sumo_started>]'
    - '&f玩家&7: &e<players_left>&7/&e<total_players>'
    - '&f战斗持续时间&7: &e<duration>[display=<sumo_started>]'
    - '&f事件持续时间&7: &e<total_duration>[display=<sumo_started>]'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  juggernaut:
    - '&7&m------------&7&m------------'
    - '&6主宰者&7: &e<juggernaut>'
    - '&f事件持续时间&7: &e<total_duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  lms:
    - '&7&m------------&7&m------------'
    - '&6最后一人站立'
    - '&f剩余玩家&7: &e<alive>&f/&e<lms_players>'
    - '&f事件持续时间&7: &e<total_duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  koth:
    - '&7&m------------&7&m------------'
    - '&6山丘之王'
    - '&f占领者&7: &e<capper> &f(&e<capper_team>&f)'
    - '&f计时器&7: &e<timer>'
    - '&f事件持续时间&7: &e<total_duration>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  queue:
    - '&7&m------------&7&m------------'
    - '&6队列&7: &e<kit>'
    - '&fElo范围&7: &e<search_range1>&f-&e<search_range2>[display=<ranked>]'
    - '&f等待时间&7: &e<wait_time>'
    - '&f排队中&7: &e<in_queue>'
    - '&7&m----------&7&m--------------[display=!<is_enderpearl_cooldown>]'
  spectator:
    - '&7&m------------&7&m------------'
    - '&6&l观战中'
    - '&f竞技场&7: &e<arena>'
    - '&f套装&7: &e<kit>'
    - '&f回合&7: &e<round>/<total_rounds>[display=<is_bestof>]'
    - '&f队伍1剩余&7: &e<own_team_left>'
    - '&f队伍2剩余&7: &e<enemy_team_left>'
    - '&f战斗持续时间&7: &e<duration>'
    - '&7&m----------&7&m--------------'
  # 当玩家在队伍中时，这将被添加到计分板中
  party-addition:
    - '&f队伍队长&7: &e<party_owner>'
    - '&f队伍成员&7:  &e<party_members>'
    - '&7&m--------------&7&m------------'
  # 如果玩家在末影珍珠冷却时间内，这将被添加
  enderpearl-cooldown-addition:
    - '&f珍珠冷却&7: &e<enderpearl_cooldown>'
    - '&7&m---------&7&m-----&7&m----------'
