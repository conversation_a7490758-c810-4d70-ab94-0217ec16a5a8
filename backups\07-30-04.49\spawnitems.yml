# 生成物品配置
# 定义了玩家在大厅中可以使用的各种功能物品
spawn-items:
  # 排位队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: DIAMOND_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa76\xa7l排位队列"
    name: ranked
    slot: 1
    command: /ranked
  # 高级队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: GOLD_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7c\xa7l高级队列"
    name: premiumqueue
    slot: 2
    command: /premiumqueue
  # 队伍战斗物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: DIAMOND_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa76\xa7l队伍战斗"
    name: partyfight
    slot: 2
    command: /party fight
    party: true
  # 队伍信息物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: SKULL_ITEM
      damage: 3
      meta:
        ==: ItemMeta
        meta-type: SKULL
        display-name: "\xa7e\xa7l队伍信息"
    name: partyinfo
    slot: 1
    command: /party info
    party: true
  # 队伍聊天物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: PAPER
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7a\xa7l队伍聊天"
    name: partychat
    slot: 6
    command: /party chat
    party: true
  # 离开队伍物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: REDSTONE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7c\xa7l离开队伍"
    name: partyleave
    slot: 7
    command: /party leave
    party: true
  # 解散队伍物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: REDSTONE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7c\xa7l解散队伍"
    name: partydisband
    slot: 7
    party-owner-only: true
    command: /party disband
    party: true
  # 队伍设置物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: WATCH
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7e\xa7l队伍设置"
    name: partysettings
    slot: 8
    party-owner-only: true
    command: /party settings
    party: true
  # 创建队伍物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: EYE_OF_ENDER
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7d\xa7l创建队伍"
    name: partycreate
    slot: 3
    command: /party create
  # 设置物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: WATCH
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa79\xa7l设置"
    name: settings
    slot: 6
    command: /settings
  # 主办事件物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: ANVIL
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa76\xa7l主办事件"
    name: hostevents
    slot: 7
    command: /events
  # 套装编辑器物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: BOOK
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa79\xa7l套装编辑器"
    name: kiteditor
    slot: 8
    command: /kiteditor
  # 非排位队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: IRON_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7e\xa7l非排位队列"
    name: unranked
    slot: 0
    command: /unranked
  # PvP机器人物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: BLAZE_POWDER
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7a\xa7lPvP机器人"
    name: botduel
    slot: 5
    command: /botduel
  # 离开队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: REDSTONE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7c\xa7l离开队列"
    name: leavequeue
    slot: 8
    queue: true
    command: /queue leave
  # 离开2v2队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: REDSTONE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7c\xa7l离开2v2队列"
    name: leave2v2
    slot: 8
    party: true
    queue: true
    command: /2v2 leave
    party-owner-only: true
  # 2v2排位队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: DIAMOND_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa76\xa7l2v2排位队列"
    name: 2v2ranked
    slot: 1
    party: true
    command: /2v2 ranked
  # 2v2非排位队列物品
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: IRON_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7e\xa7l2v2非排位队列"
    name: 2v2unranked
    slot: 0
    party: true
    command: /2v2
  # 离开2v2队列物品（重复项）
  - ==: SpawnItem
    item:
      ==: org.bukkit.inventory.ItemStack
      type: REDSTONE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "\xa7c\xa7l离开2v2队列"
    name: 2v2leave
    slot: 8
    command: /2v2 leave
    party: true
    queue: true
    party-owner-only: true