# StrikePractice 配置文件
# (支持所有版本 1.8+)
# 在报告错误之前，请检查错误不是由其他插件或您的自定义spigot引起的
# 仅官方支持常规spigot和paper
# 如果您想要PvP机器人和回放功能，您必须有兼容版本的Citizens插件！
# 需要Java 8或更高版本

#
# #
# # 请报告所有错误！
# #
#
#
#                                             #
# ---------------------------------------------#
# 服务器设置和杂项                              #
# ---------------------------------------------#
#                                             #
# 大多数消息中的前缀
prefix: '&7[&aStrikePractice&7] '
# 当有更新可用时，插件是否应该通知拥有"strikepractice.update"权限的玩家
notify-updates: true
# 以秒为单位
# 战斗结束后多少秒将玩家传送回大厅
wait-before-teleport: 3
# 玩家在从ffa竞技场传送出去之前必须等待多少秒
# 如果玩家移动或受到伤害，传送将被取消
wait-before-leaving-ffa-arena: 10
# /night, /day, /ping 等。使用 /practicecommands 查看所有命令
enable-practice-commands: true
# 如果延迟太高/太低，调整延迟
ping-multiplier: 1
# 右键点击生成物品之间的冷却时间
spawnitem-cooldown: 500
# 如果有竞技场，将'arenas-world'堆叠x次
# 如果设置为2，'arenas-world'将被堆叠2次（总共3个竞技场世界）
# 即使不是原始竞技场，这些竞技场上也可以使用回放功能
autostack-arenas-on-enable: 2
# 插件是否应该为FFA竞技场使用复制的世界
# 建议设置为'true'，因为它可以保护FFA竞技场免受服务器崩溃等永久性损坏
# 如果autostack-arenas-on-enable为0或更少，则自动为false
use-copied-arena-for-ffa: true
# 插件是否应该在主竞技场世界中使用建造竞技场
# 保持false将在服务器崩溃时不会出现回滚问题
# 只会使用建造竞技场的副本
build-fights-in-main-arenas-world: false
# 竞技场应该位于的世界名称，如果启用，此竞技场世界将被堆叠
# 您仍然可以在不同的世界中创建竞技场
arenas-world: Duels
# 保持区块票证（如果支持，MC 1.14+）
keep-arenas-loaded: true
# 竞技场世界是否应该是虚空世界
# 如果区块生成导致问题，请禁用
empty-arenas-world: true
# 使用FAWE（FastAsyncWorldEdit）进行竞技场重置
use-fawe-reset: true
arenas-world-allow-monsters: false
arenas-world-allow-animals: false
# 使用更好的设置并忽略配置的某些部分
# 某些功能被禁用或可能无法正常工作
# 结果是更好的性能
performance-mode: true
# 默认语言，在玩家选择其他语言之前
# 您必须在messages.yml中有该语言
default-language: english
# 允许可点击消息
# 队伍加入、战斗物品栏和决斗接受
clickable-messages: true
# 将可点击的消息
clickable-message: '&b&c>&e点击这里&c<'
# 简单日期格式
date-format: dd/MM HH:mm:ss
# 占位符格式
current-time-format: HH:mm
# 在消息等中支持十六进制颜色代码
hex-support: true
# 如果启用，我们添加"plain_<placeholder>"，它与原始占位符相同但没有颜色代码
# 例如 %strikepractice_plain_hits_difference% -> 无颜色 %strikepractice_hits_difference%
plain-placeholderapi-placeholders: false
# 如果您想增加或减少服务器时间
# 例如，如果服务器时间显示15:00，将此设置为-2将导致13:00
server-time-increase: 0
# 除非您已禁用自动区块卸载，否则保持false。否则，这将导致延迟
keep-chunks-loaded: false
# 如果玩家在出生点掉入虚空，自动将玩家传送回大厅
teleport-void-spawn: false
# 玩家加入服务器时是否应该被传送到大厅
# 如果禁用此功能，玩家在加入时不会收到生成物品
join-lobby-teleport: true
#
# 在"杂项设置"部分中有更多类似设置。
#
#                                             #
# ---------------------------------------------#
# 观战/观众                                    #
# ---------------------------------------------#
#                                             #
# 是否应该启用观战
allow-spectating: true
# 玩家是否应该作为观众观战事件（锦标赛（1v1锦标赛）和相扑）或在 /sumo setlobby 和 /brackets setlobby
allow-brackets-spectating: true
# 在决斗中死亡时禁用观战几秒钟
# no-duel-spectating: true
# 玩家死亡后多少秒应该进入观众模式（如果启用）
# 建议为0，其他值可能会导致问题
wait-before-spectator: 0
# 传送器物品将包含所有当前战斗的玩家
spectator-teleporter-distance: 150
# 防止玩家飞得太靠近战斗玩家
spectator-stay-away: false
hide-other-spectators: false
# 您可以使用"SPECTATOR"，但物品将停止工作
# 您可以将离开物品名称更改为类似"&c使用 /leave 离开观众模式"
spectator-gamemode: SURVIVAL
# 如果为false，观众可以飞到任何地方，如果为true，他们必须在竞技场角落内
# 如果未设置角落，他们必须保持在竞技场中心200个方块内
spectator-keep-inside-arena: true
spectator-menu: '&a观战'
spectator-menu-kit: '&6套装: &e<kit>'
spectator-menu-arena: '&6竞技场: &e<arena>'
spectator-menu-duration: '&6持续时间: &e<duration>'
# 观众检查周期（以tick为单位）
spectator-timer-period: 20
# 使用AIR禁用物品
spectator-teleport-item: COMPASS
spectator-teleport-name: '&a传送器'
# 传送物品栏的标题
spectator-teleport-title: '&a传送'
# 玩家也可以使用 /leave 离开观众模式
spectator-leave-item: RED_ROSE
spectator-leave-name: '&c离开观众模式'
spectator-inventory-view: true
# 如果启用，StrikePractice将不会对玩家物品栏进行任何修改，例如给予新物品、清空物品栏等。
# 使用此设置，玩家可以带着自己的物品参加战斗。
disable-inventory-modifications: false
# 观战时阻止命令
spectator-block-all-commands: false
spectator-blocked-commands:
- /kit
- /essentials:kit
- /examplepluginname:exampleblockedcommand
#                                             #
# ---------------------------------------------#
# 套装/战斗套装                                #
# ---------------------------------------------#
#                                             #
# 连击模式下的攻击延迟
# Minecraft中的默认攻击延迟是20
combo-hit-delay: 2
# 如果您有自定义攻击延迟（例如19），请更改此项
# 注意：这只会在连击套装后更改玩家的攻击延迟，在其他战斗中被忽略
# 因此，如果您更改此项，请确保您有其他插件或您的spigot每次都这样做
# Minecraft中的默认攻击延迟是20
default-hit-delay: 20
preview:
  # 玩家是否应该能够在套装选择器物品栏中预览所有套装
  shift-click-preview: true
  # 预览套装选择器物品栏的标题
  select-kit-title: '&9选择要预览的套装'
  # 预览物品栏的标题
  title: '&9套装预览'
  # 套装的药水效果
  pots: '&c效果:'
  potions: '&b<type> - <amplifier> - <duration> 分钟'
  # 显示套装是否为排位套装
  elo-item: GOLD_INGOT
  elo-name: '&cELO: <value>'
# 其他内容将从'custom-kit:'部分获取
# 为玩家提供的一种自定义游戏模式
custom-kit:
  enabled: true
  # 自定义套装的默认名称
  default-name: '&9自定义套装'
  # 默认图标
  icon-material: DIAMOND_SWORD
  # 如果您希望玩家拥有预制套装，请在此处设置套装名称
  premade: premadecustomkit
  # 物品栏的第二行
  second-line: STAINED_GLASS_PANE:2
  # 主物品栏的标题
  main-title: '&c自定义套装'
  # 更改自定义套装名称时发送给玩家的消息
  # <name> 将被替换为自定义套装的新名称
  new-name: '&6新名称: &r<name>'
  # 玩家可以编辑其自定义套装图标的物品栏标题
  custom-kit-name: '&c自定义套装图标'
  # 玩家可以编辑其自定义套装盔甲的物品栏标题
  helmets: '&c自定义套装头盔'
  chestplates: '&c自定义套装胸甲'
  leggings: '&c自定义套装护腿'
  boots: '&c自定义套装靴子'
  # 标题中会有一个数字（指槽位）
  item: '&c自定义套装物品: '
  # 物品栏中的空气应该如何显示
  air: '&6空气'
  # <value> 将被替换为'yes-or-true:'或'no-or-false'的翻译
  horse: '&c马: <value>'
  combo: '&c连击: <value>'
  bow: '&c弓: <value>'
  build: '&c建造: <value>'
  # 每个选项的图标
  horse-item: GOLD_BARDING
  combo-item: CLAY
  bow-item: BOW
  build-item: DIAMOND_PICKAXE
  # 插件是否应该使用通过 /customkit items 设置的物品或内置物品
  # 执行 /customkit items 时自动更改
  use-custom-items: false
  # 当给玩家这些套装之一时，将给予玩家的自定义套装
  replaces-kits:
  - thisKitWillBeReplacedWithThePlayersCustomKit
#                                             #
# ---------------------------------------------#
# 套装编辑器                                   #
# ---------------------------------------------#
#                                             #
# 套装选择器物品栏中是否应该有套装编辑器物品
kit-editor-in-kit-selector: false
# 物品名称
kit-selector-editor-name: '&3自定义套装编辑器'
# 物品材料
kit-selector-editor-material: BOOK
# 如果没有编辑过的套装，不要给书
give-kit-automatically: true
# 套装编辑器选择器物品栏的标题
kit-editor-title: '&3编辑套装'
# 玩家是否应该能够通过打开铁砧来打开 /kiteditor（选择要编辑的套装）
kit-editor-anvil: false
# 保存玩家套装的告示牌第一行
kit-editor-save-sing-line-1: '[保存套装]'
# 离开套装编辑器地点的告示牌第一行
kit-editor-leave-line-1: '[离开编辑器]'
# 重置编辑套装的告示牌第一行
kit-editor-reset-kit-line-1: '[重置套装]'
# 如果为true，玩家只能编辑套装的一个版本
legacy-kit-editor: false
# GUI套装编辑器
kit-editor:
  # 物品栏标题
  title: '&7管理套装布局'
  allow-editing-armor: false
  # 套装名称应该如何格式化
  # <kit> 是基础套装
  # <number> 是套装的版本（1-5）
  kit-format: <kit> &r# &a<number>
  # 默认布局书籍名称
  # <kit> 是"基础套装"名称
  default-kit: '&e默认套装'
  # GUI中的物品
  save:
    item: ENCHANTED_BOOK
    name: '&2保存套装 <kit>'
  # 来自messages.yml的"kit-saved"消息
  load:
    item: CHEST
    name: '&2加载套装 <kit>'
    message: '&a套装 <kit> 已加载。'
  rename:
    item: NAME_TAG
    name: '&2重命名套装 <kit>'
    message: '&a请在聊天中输入新名称。'
    # 成功重命名时
    success: '&a套装已重命名为 <kit>。'
  delete:
    item: HOPPER
    name: '&2删除套装 &2<kit>'
    message: '&a套装删除成功。'
#                                             #
# ---------------------------------------------#
# 战斗/对战                                    #
# ---------------------------------------------#
#                                             #
# 系统在基于FFA的战斗中选择中心周围位置时使用的半径
circle-radius: 10
bow-health: true
# * 表示所有套装
# 否则用逗号分隔，例如 'builduhc, sg'
bow-health-kits: '*'
# - 'example console command'
# - 'player command: /example player command'
# 建造套装的建造限制
# 从竞技场中心计算
build-limit: 25
winner-commands:
  #    - 'pay <player> 5'
  bot-duel: []
  party-split: []
  party-ffa: []
  party-bots: []
  party-vs-party: []
# 队伍1的前缀
# 己方队伍
team1-prefix: '&a'
# 队伍2的前缀
# 敌方队伍
team2-prefix: '&c'
# 许多物品栏中返回上一个物品栏或关闭当前物品栏的物品名称
back-button-name: '&c返回'
# 预览某人请求与玩家或其队伍战斗的自定义套装的消息
preview-custom-kit: '&6点击这里预览自定义套装'
# 如果玩家正在战斗，当他们的y坐标低于0时会立即死亡
# 适用于空岛战争和挖掘游戏
insta-void: true
# 自行承担风险启用
insta-void-async: false
# 仅在Paper 1.13+上支持（不是常规spigot）
async-teleport: true
# 以分钟为单位
ffa-reset-delay: 30
# 玩家死亡时是否会被传送到大厅或重新加入ffa
ffa-rejoin-automatically: true
rematch-item:
  # 玩家是否应该获得重新匹配生成物品
  enabled: true
  # 多少秒后应该移除生成物品
  remove-after: 10
  # 槽位 (0-8)
  slot: 8
  # 物品
  item:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: DIAMOND
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: '{"text":"","extra":[{"text":"重新匹配 <opponent>","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"light_purple","bold":true}]}'
match:
  # 插件是否应该在战斗中阻止所有命令
  block-all-commands: false
  # 否则这将帮助您
  blocked-commands:
  - /kit
  - /essentials:kit
  - /exampleblockedcommand
  - /examplepluginname:exampleblockedcommand
# 以秒为单位
# 用于所有类型的战斗
countdown-time: 4
# 玩家死亡后的倒计时
# 如果启用了 /battlekit deathcountdown <kit>
respawn-countdown-time: 3
# 0表示"开始"，-1表示禁用
ffa-countdown: 0
# "false"表示无声音
# 例如这样：
# Countdown-sound: false
countdown-sound: NOTE_PLING
countdown-inventory-update: true
match-start-sound: LEVEL_UP
# 以分钟为单位
fight-duration-limit: 20
# 物品栏标题
playback-gui-title: '&b回放比赛'
# 套装选择器物品栏标题
inventory-title: '&b选择套装'
map-selector:
  enabled: true
  title: '&b选择地图'
  random-map-button:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: WHITE_WOOL
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: '{"text":"","extra":[{"text":"随机地图","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"blue","bold":true}]}'
#
# 回放相关
#
# 以秒为单位
# 比赛录制的最大时间，避免玩家创建过大的回放文件
max-recording-time: 600
# 插件是否应该录制所有战斗（包括排位和非排位决斗）
record-all-fights: true
# 插件是否应该只录制排位决斗
record-elo-fights: true
# 如果为true且比赛被录制，决斗后将向玩家发送'kill-cam'消息
kill-cam: true
playback-bot-name: '[回放]<player>'
# 插件应该在多少天后删除录制的回放
# 例如，设置为0.5表示12小时
remove-record-after: 7.0
#                                             #
# ---------------------------------------------#
# 队列                                         #
# ---------------------------------------------#
#                                             #
# 非排位物品栏的标题，或者如果separate-queues为false时的队列物品栏标题
queue-inventory-title: '&3队列'
ranked-queue-inventory-title: '&3排位队列'
premium-queue-inventory-title: '&c高级队列'
2v2-ranked-queue-inventory-title: '&3排位2v2队列'
2v2-queue-inventory-title: '&32v2队列'

premium-permission-message: '&c您没有访问高级队列的权限'
# 用于退出队列的物品名称
quit-item-name: '&c&L离开当前队列。'
# 在队列物品栏中启用或禁用离开队列物品
leave-queue-item: false
# 非排位和排位是否应该在单独的物品栏中
separate-queues: true
# 加入排位队列时允许的最大延迟
max-ranked-queue-ping: 500
limit-rankeds: true
limit-unrankeds: false
rankeds-per-day: 20
unrankeds-per-day: 50
unlimited: unlimited
# 启用战斗之间的冷却时间
queue-cooldown: false
# 以秒为单位
queue-cooldown-time: 10
# 启用排位战斗之间的冷却时间
elo-queue-cooldown: false
# 以秒为单位
elo-queue-cooldown-time: 20
# 只是为了减少垃圾信息
# 决斗邀请之间的冷却时间
# 以毫秒为单位
request-cooldown: 5000
# 每个竞技场每tick（延迟）的最大方块变化数
max-block-changes-per-tick-per-arena: 10
# 默认情况下，一场战斗的方块限制为2000 +（玩家数量 * 250）（大约）
# 如果您想绕过它，请在下面设置自定义限制。所有方块变化都计算在内。
# max-block-changes: 20000
experimental-block-updater: true
# 在队列物品栏/物品栏的物品中显示
in-queue: '&d队列中的玩家: &e<players>'
# <wins_kit> <losses_kit> 需要SQL数据库，可能会增加大型服务器的数据库负载
in-match: '&d战斗中的玩家: &e<players>\n \n&d获胜比赛: &e<wins_<raw_kit>>\n&d失败比赛: &e<losses_<raw_kit>>'
change-icon-amount: true
ranked:
  # 玩家在加入排位队列之前必须有多少击杀
  kills-required: 10
  # 多少秒后应该增加范围
  elo-range-time: 5
  # 每次应该增加多少范围
  elo-range-increase: 50
  # 多少秒后玩家应该能够与此状态下的任何人战斗
  anyone-after: 60
#                                             #
# ---------------------------------------------#
# 赛后物品栏预览                               #
# ---------------------------------------------#
#                                             #
fight-inventory:
  rows: 6
  # 插件应该从哪里开始显示物品栏内容
  item-start-slot: 1
  # 插件应该在哪里显示头盔物品
  helmet-slot: 36
  # 胸甲同样
  chestplate-slot: 37
  # 护腿同样
  leggings-slot: 38
  # 靴子同样
  boots-slot: 39
  # 玩家的饥饿值
  food-slot: 48
  playback-slot: 46
  # 玩家的生命值
  health-slot: 47
  # 玩家的药水效果
  pots-slot: 50
  # 剩余生命药水数量
  pots-left-slot: 49
  # <food> 将被替换为玩家的饥饿值
  food: '&6饥饿值: <food>/20'
  # <health> 将被替换为玩家的生命值
  health: '&6生命值: <health>/20'
  # 如果玩家死亡，这将在他们的'生命值'物品中显示
  dead: '&c死亡'
  # 效果物品的名称
  effect-displayname: '&7药水效果'
  # 说明的第一行
  pots: '&3类型 - 等级 - 持续时间'
  # 效果应该如何显示
  # <type> 将被替换为药水效果类型
  # <amplifier> 将被替换为药水效果的等级
  # <duration> 将被替换为药水效果的剩余持续时间
  effect: '&b<type> <amplifier> (<duration> 分钟)'
  # 物品栏标题
  inventory: '&7<player> 的物品栏'
  # 剩余生命药水数量
  pots-left: '&c剩余生命药水: <potions>'
  match-stats-lore:
  - '&d命中: &e<hits>'
  - '&d最长连击: &e<combo>'
  - '&d投掷药水: &e<potions_thrown>'
  - '&d错过药水: &e<potions_missed>'
  - '&d治疗准确率: &e<potion_accuracy>'
  soups-left: '&c剩余汤: &e<soups>'
  next-inventory-name: '&d显示 <player> 的物品栏'
  next-inventory-slot: 53
  next-inventory-item: PAPER
spectator-inventory-messages: true
inventory-message-your: '&6您的队伍: '
inventory-message-opponent: '&6对手队伍: '
winner-inventories: '&a获胜者物品栏:'
loser-inventories: '&c失败者物品栏:'
# - '&7&m----------------------------'
# - '&6&l点击查看物品栏'
# - '&a获胜者: &f<winner>&7[&e<winner_pots>&7]'
# - '&c失败者: &f<loser>&7[&e<loser_pots>&7]'
# - '&7&m----------------------------'
#  或者
#  - '&e点击查看物品栏'
#  - '&a获胜者: <winner>&7[&e<winner_pots>&7] - &c失败者: <loser>[&e<loser_pots>&7]'
#
fight-inventory-message:
- '&7持续时间: &a<duration>'
- '&7竞技场: <arena> &7- 套装: <kit>'
- '&7&m----------------------------'
- '&6&l点击查看物品栏'
- '&a获胜者: &f<winner>&7[&e<winner_pots>&7]'
- '&c失败者: &f<loser>&7[&e<loser_pots>&7]'
- '&7&m----------------------------'
fight-inventory-player: '&e<player>'
# 发送多个可点击物品栏时使用
inventory-separator: '&e, '
#                                             #
# ---------------------------------------------#
#  PvP机器人                                  #
# ---------------------------------------------#
#                                             #
# pvp机器人的最大攻击范围
bot-fast-potions: true
enable-bot-pearling: true
# 机器人的攻击范围
# 如果机器人导致任何错误或bug，请启用此项
# 启用将略微降低性能
main-thread-bot: false
# 如果禁用，玩家只会受到0.5颗心的伤害
# 此选项只是以防它不能正常工作
fix-bot-damage: true
# 如果机器人在您的spigot上不受击退，请启用此项
# 将帮助一些自定义spigot
# 如果击退很奇怪，也会有帮助
fix-bot-kb: true
# 水平击退
bot-kb-amount: 0.27
# 垂直击退
# 最大值
bot-kb-amount-upward: 0.38
bot-kb-amount-upward-min: 0.27
# pvp机器人应该如何命名，<player> 将被替换为玩家的名称
bot-name: BOT_<player>
# 不建议将此设置为false，因为它可能并且很可能会导致错误
always-same-skin: true
bot-skin: steve
bot-teleport-error-fix: true
bot-difficulty:
  # 难度选择器的标题
  title: '&a机器人难度'
  # 难度选择器的大小
  # 图标将在此物品栏的中间
  inventory-size: 9
  easy:
    reach: 2.8
    icon:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: LIME_STAINED_GLASS_PANE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: §a§l简单
        lore:
        - '{"text":"","extra":[{"text":"攻击距离: ","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"yellow","bold":false},{"text":"2.7","italic":false,"color":"green"}]}'
  normal:
    # 攻击范围的工作方式不同，不对应真实玩家的攻击距离（或至少看起来不像）
    # 因此我们在物品说明中撒谎。
    # 您也可以对机器人CPS撒谎
    reach: 3.9
    icon:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: YELLOW_STAINED_GLASS_PANE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: §a§l普通
        lore:
        - '{"text":"","extra":[{"text":"攻击距离: ","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"yellow","bold":false},{"text":"3","italic":false,"color":"green"}]}'
  hard:
    reach: 5.6
    icon:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: ORANGE_STAINED_GLASS_PANE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: §a§l困难
        lore:
        - '{"text":"","extra":[{"text":"攻击距离: ","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"yellow","bold":false},{"text":"3.2","italic":false,"color":"green"}]}'
  hacker:
    reach: 6
    icon:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: RED_STAINED_GLASS_PANE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: §a§l黑客
        lore:
        - '{"text":"","extra":[{"text":"攻击距离 ","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"yellow","bold":false},{"text":"4.3","italic":false,"color":"green"}]}'
#                                             #
# ---------------------------------------------#
#  玩家设置                                    #
# ---------------------------------------------#
#                                             #
# 确保您已设置 /sprac spawncorner1 和 /sprac spawncorner2
# 一个在上面，另一个在相对角落的下面
player-spawn-hider:
  # 如果在这里和玩家设置中启用，此区域内的玩家将被隐藏
  enabled: false
  spawn-corner1: 100.0, 204.0, 100.0, 0.0, 0.0, Bowling
  spawn-corner2: -100.0, 4.0, -100.0, 0.0, 0.0, Bowling
player-settings:
  inventory-size: 27
  title: '&a&l设置'
  language-item: PAPER
  language-name: '&e更改语言'
  language-slot: 14
  duel-requests-item: BLAZE_ROD
  duel-requests-name: '&e决斗设置'
  duel-requests-slot: 12
  scoreboard-item: BOOK
  scoreboard-name: '&e切换计分板'
  scoreboard-slot: 10
  hide-players-item: SLIME_BALL
  hide-players-name: '&e切换玩家隐藏'
  hide-players-slot: 16
  # 每个设置的默认值
  default-values:
    scoreboard-disabled: false
    hide-players: false
    duel-requests-disabled: false
    admin-scoreboard: false
admin-scoreboard-color1: '&6'
admin-scoreboard-color2: '&e'
language:
  # 如果有翻译可用，将使用玩家的Minecraft语言
  automatic-language: true
  # 物品栏标题
  title: '&3选择您的语言'
  # 当前语言物品
  your-language-item: WOOL
  # 当前语言名称
  your-language: '&b&l您的语言: <language>'
  # 物品栏大小
  inv-size: 18
# /duel（不带任何参数）是否应该打开决斗物品栏
duel-inventory: false
# 物品栏标题
duel-inventory-title: '&3决斗'
# 决斗物品名称
duel-inventory-duel: '&3与玩家决斗'
# 待处理请求物品名称
duel-inventory-pending: '&3待处理的决斗请求'
# 玩家禁用决斗请求时的物品名称
duel-inventory-toggle-disabled: '&3切换决斗请求（已禁用）'
# 玩家启用决斗请求时的物品名称
duel-inventory-toggle-enabled: '&3切换决斗请求（已启用）'
# /duel <player> <rounds> [kit] 中的最大回合数
duel-max-rounds: 20
#                                             #
# ---------------------------------------------#
# 杂项设置 - 启用/禁用功能                      #
# 没有自己类别的设置                            #
# ---------------------------------------------#
#                                             #
#
# 默认设置相当不错
#
# 是否应该在团队战斗中启用彩色名称标签（例如，队伍分割、队伍对队伍和koth）
enable-colored-names: true
# 皮革盔甲是否总是着色为红色/蓝色
any-kit-colored-armor: true
# 如果服务器在玩家战斗时崩溃或停止，插件是否应该回滚竞技场
# 这将回滚所有可以用套装物品栏放置的方块，
# 不仅仅是实际放置的方块
# 例如，如果套装有圆石，corner1和corner2之间的每一个圆石都将被设置
rollback-on-enable-if-crashed: false
# 插件是否应该防止草的传播
disable-grass-spread: true
# 插件是否应该在战斗中防止合成
disable-crafting-in-fight: true
no-hunger-in-lobby: true
# 插件是否应该防止下雨
anti-rain: true
# 插件是否应该防止火焰传播
anti-fire: true
# 对一些末影珍珠故障的简单修复
# 如果这不起作用，请尝试其他珍珠修复插件
enderpearl-fix: true
# 插件是否应该防止熔岩和水生成圆石
anti-lava-generator: false
# 插件是否应该在箭落地时移除箭
remove-arrows: true
# 如果套装启用了 /battlekit bedwars 并且玩家有弓，
# 如果他们还没有箭，他们将每5秒获得一支箭
bedwars-reload-arrows: true
bedwars-bed-break-sound: ENDERDRAGON_GROWL
# 在bedwars中玩家的床被破坏时运行的命令
# <target> 将被替换为失败队伍的玩家名称
# <player> 将被替换为破坏床的玩家名称
bedwars-bed-break-commands:
- title <target> title {"text":"您的床已被摧毁！","color":"red"}
- title <target> subtitle {"text":"您将不再重生！","color":"red"}
# 如果玩家吃了名称包含"golden head"或"goldenhead"（不区分大小写）的金苹果，插件是否应该给予额外效果
golden-heads: true
bridges-instant-golden-apples: true
# 玩家应该在聊天中输入搜索还是在虚假告示牌中
# 可能对1.9+玩家不起作用
enable-sign-search: false
# 黄金头颅效果
golden-head-effects:
- ==: PotionEffect
  effect: minecraft:absorption
  duration: 1800
  amplifier: 0
  ambient: true
  has-particles: true
  has-icon: true
- ==: PotionEffect
  effect: minecraft:regeneration
  duration: 180
  amplifier: 1
  ambient: true
  has-particles: true
  has-icon: true
disable-spawn-damage: true
# 当玩家不在建造战斗中时，插件是否应该防止建造
# 管理员仍然可以在创造模式下建造
disable-building-without-build-kit: true
# 如果为true，玩家不能在竞技场外建造
# 仅适用于有角落的竞技场，并且当玩家在战斗中时
disable-building-outside-arenas: false
# 如果套装名称包含"uhc"，它将禁用生命恢复
disable-regen-with-uhc-kits: true
# 插件是否应该防止玩家在不战斗时丢弃物品
prevent-item-dropping: true
# 在战斗中防止丢弃剑/弓/铲子和其他工具
prevent-tool-dropping: true
# 插件是否应该在启动时移除所有物品掉落
remove-all-drops-on-startup: true
# 是否取消插件无法重置的爆炸
cancel-explosions: true
# 如果启用，汤将治疗3.5颗心
insta-soup: true
# 玩家破坏雪块后是否会自动在物品栏中获得雪球
snowball-on-snow-break: true
# 用户喝完后是否移除瓶子
remove-bottles: true
# 以秒为单位
# 物品掉落时应该在地面上停留多长时间
# 只会影响比赛和pvp事件中发生的死亡和掉落事件
remove-drops: 3
death:
  # 如果启用，死亡消息将被禁用
  disable-message: true
  # 当有人死亡时为附近玩家显示闪电
  lightning: true
  # 将修复一些其他插件导致的问题
  # 如果您的spigot出现相关错误，请禁用
  disable-keep-inventory: true
  respawn: true
  respawn-in-arena: true
economy:
  enabled: false
  # 击杀给钱
  kill-give: false
  # 数量
  kill-amount: 10
  # 玩家死亡时扣钱
  death-withdraw: false
  # 数量
  withdraw-amount: 5
# 如果玩家离开corner1和corner2内的区域，玩家开始受到伤害
# 如果启用，性能会略有下降
storm-wall-outside-arenas: false
#                                             #
# ---------------------------------------------#
#  火球设置                                    #
# ---------------------------------------------#
#                                             #
fireball:
  # 0 = 无伤害但有"伤害效果"，1 = 正常伤害
  shooter-damage-multiplier: 0
  knockback:
    # 被击退实体的半径
    radius:
      x: 2
      y: 2
      z: 2
    # 火球跳跃中使用的倍数
    multiplier: 1.5
    # 火球跳跃高度使用的力量
    y-force: 1.5
  cooldown:
    enabled: true
    # 冷却时间（秒）
    cooldown: 2
    # 冷却时间格式
    format: '0'
    # 名称更新频率
    # 以tick为单位
    interval: 2
    # 玩家冷却时手持末影珍珠的名称
    # 使用 <time> 表示剩余时间
    fireball-name: '&b冷却时间: &c<time> 秒'
#                                             #
# ---------------------------------------------#
#  TNT设置                                    #
# ---------------------------------------------#
#                                             #
tnt:
  # 0 = 无伤害但有"伤害效果"，1 = 正常伤害
  source-damage-multiplier: 0
  # 当套装的TNTJumps设置为true时应用这些设置
  knockback:
    # 被击退实体的半径
    radius:
      x: 2
      y: 2
      z: 2
    # TNT跳跃中使用的倍数
    multiplier: 1.5
    # TNT跳跃高度使用的力量
    y-force: 1.5
#                                             #
# ---------------------------------------------#
#  末影珍珠冷却                                #
# ---------------------------------------------#
#                                             #
enderpearl-cooldown:
  enabled: true
  # 冷却时间是否显示为玩家的经验等级/经验条
  modify-exp-level: true
  change-item-name: true
  # 冷却时间（秒）
  cooldown: 10
  # 冷却时间格式
  format: '0'
  # 名称更新频率
  # 以tick为单位
  interval: 2
  # 玩家冷却时手持末影珍珠的名称
  # 使用 <time> 表示剩余时间
  pearl-name: '&b冷却时间: &c<time> 秒'
# 查看计分板部分的末影珍珠计分板行
#                                             #
# ---------------------------------------------#
#  数据库                                      #
# ---------------------------------------------#
#
# 注意：在旧的MC服务器（1.8）上，您可能想使用MySQL/MariaDB 5.7.x而不是8+
#
database:
  # 您想将统计数据保存在SQL数据库中还是保存在每个玩家的文件中
  mysql: false
  host: localhost
  port: 3306
  user: practice
  password: strikepracticeisbest
  # 数据库名称。如果您需要任何额外参数，可以尝试将它们放在此后（例如"name: strikepractice?someParam=value"）
  name: strikepractice
# 是否应该将所有战斗（包括elo）保存到mysql的'fights'表中
save-all-fights: false
# 是否只应该保存elo战斗
save-elo-fights: false
discord:
  enable-webhooks: false
  # discord服务器设置 -> 集成 -> webhooks -> 创建新的并复制URL
  # 将URL粘贴到下面并配置discord-webhooks.json
  fight-webhook-url: https://discord.com/api/webhooks/123
  webhook-types:
  - ranked
  - unranked
# - botduel
# 如果您使用的是旧版本的strike-web插件，请启用此项
# 如果启用此项，胜利/失败将不起作用
legacy-fight-format: false
match-link-after-fight: true
# 示例（<started>作为战斗ID）
# match-link: '&9比赛链接: https://sp-stats.toppe.dev/fight/<started>/'
match-link: '&9比赛链接: https://demo.legendeffects.co.uk/StrikePractice-Web'
# 如果您想在不使用mysql的情况下使用顶级占位符，请启用
# 如果您使用平面文件且不需要顶级占位符，请禁用以获得稍好的性能
top-placeholders-require-mysql: false
#                                             #
# ---------------------------------------------#
# 统计数据                                     #
# ---------------------------------------------#
#                                             #
# 在线玩家的统计数据应该多久保存一次以避免服务器崩溃重置它们，以秒为单位
player-stats-auto-save-period: 600
# 一些统计数据（如"获胜战斗"、"nodebuff失败"等）从数据库查询
# 设置这些统计数据的最大年龄（以秒为单位）。如果您的数据库因此受到影响，您可以增加
cached-stats-max-age: 300
# /stats <离线玩家> 是否有效
offline-player-stats: true
# 默认起始elo
# 如果您使用MySQL并在已经制作套装后更改此项，您可能需要重新创建列
starting-elo: 1000
# 2表示elo变化翻倍，0.5表示变为默认值的一半等
elo-change-modifier: 1.0
# 如果为false，全局elo是所有套装的平均elo。如果为true，全局elo是与起始elo不同的套装的平均elo
global-elo-ignore-starting-elo: false
# 使用命令 /stats 时应该显示哪些统计数据
show-stats:
  deaths: true
  kills: true
  elo: true
  party-wins: true
  lms: true
  brackets: true
  global-elo: true
sign-stats:
  # 第一行必须是这个
  # 只是显示特定统计数据的行
  # 我认为这很容易理解
  line-1: '&3[&4统计&3 ]'
  line-2: '&4点击我！'
  # <player> 将被替换为显示统计数据的玩家名称
  player-line: '&5<player>'
  brackets-line: '&5锦标赛胜利'
  kills-line: '&5击杀'
  deaths-line: '&5死亡'
  lms-line: '&5LMS胜利'
  party-wins-line: '&5队伍胜利'
  elo-line: '&5<kit> Elo'
  global-elo-line: '&5全局Elo'
  # 值（例如击杀数）
  value: '&3<value>'
#                                             #
# ---------------------------------------------#
# 击退设置（可选）                             #
# ---------------------------------------------#
#                                             #
knockback:
  # 设置为true以启用击退修改
  # 如果为false，StrikePractice不会对击退和命中检测做任何事情
  enabled: false
  # 如果为true，只有当玩家使用连击套装时才修改击退
  only-combo: false
  # 您可以复制并添加-max（例如air-horizontal-max: 0.98）进行随机化
  # 可能是非常糟糕的击退，只是猜测了一些值
  default:
    air-horizontal: 0.93
    air-vertical: 0.93
    horizontal: 0.82
    vertical: 0.88
  # 随机化示例
  # air-horizontal-max: 1.01
  # air-vertical-max: 1.0
  # horizontal-max: 0.91
  # vertical-max: 0.98
  # 在连击套装中
  combo:
    air-horizontal: 0.82
    air-vertical: 0.82
    horizontal: 0.75
    vertical: 0.78
# 对于"sumo"套装
# sumo:
# air-horizontal: 1
# air-vertical: 1
# horizontal: 0.95
# vertical: 0.97
#                                             #
# ---------------------------------------------#
# Elo奖励和Elo等级                            #
# ---------------------------------------------#
#                                             #
# 玩家可能因同一等级获得多次奖励
elo-rewards:
  # 等级名称
  example_level:
    # 所需elo
    elo: 1200
    # 要运行的命令
    commands:
    - examplecommand <player> <level>
elo-ranks:
  enabled: true
  # 如果等级消息应该每次都发送，设置为true；如果只在玩家等级改变时发送，设置为false
  send-everytime: false
  # 基于玩家的全局elo计算
  # elo范围不应重叠
  ranks:
    iron1:
      name: '&7 黑铁 I'
      elo-range: 0-99
    iron2:
      name: '&7 黑铁 II'
      elo-range: 100-199
    iron3:
      name: '&7 黑铁 III'
      elo-range: 200-299
    iron4:
      name: '&7 黑铁 IV'
      elo-range: 300-399
    iron5:
      name: '&7 黑铁 V'
      elo-range: 400-499
    silver1:
      name: '&7白银 I'
      elo-range: 500-599
    silver2:
      name: '&7白银 II'
      elo-range: 600-699
    silver3:
      name: '&7白银 III'
      elo-range: 700-799
    silver4:
      name: '&7白银 IV'
      elo-range: 800-899
    silver5:
      name: '&7白银 V'
      elo-range: 900-999
    gold1:
      name: '&6黄金 I'
      elo-range: 1000-1149
    gold2:
      name: '&6黄金 II'
      elo-range: 1150-1299
    gold3:
      name: '&6黄金 III'
      elo-range: 1300-1449
    gold4:
      name: '&6黄金 IV'
      elo-range: 1450-1599
    gold5:
      name: '&6黄金 V'
      elo-range: 1600-1749
    diamond1:
      name: '&b钻石 I'
      elo-range: 1750-1949
    diamond2:
      name: '&b钻石 II'
      elo-range: 1950-2149
    diamond3:
      name: '&b钻石 III'
      elo-range: 2150-2349
    diamond4:
      name: '&b钻石 IV'
      elo-range: 2350-2549
    diamond5:
      name: '&b钻石 V'
      elo-range: 2550-2749
    emerald1:
      name: '&a荣耀 I'
      elo-range: 2750-2999
    emerald2:
      name: '&a荣耀 II'
      elo-range: 3000-3499
    emerald3:
      name: '&a荣耀 III'
      elo-range: 3500-3999
    emerald4:
      name: '&a荣耀王者 I'
      elo-range: 4000-4999
    emerald5:
      name: '&a最强王者 '
      elo-range: 6000-9999999
#                                             #
# ---------------------------------------------#
# 队伍相关                                     #
# ---------------------------------------------#
#                                             #
#
# 我知道配置的这部分看起来很丑
#
party:
  info-command:
  - '&6队伍队长: &e<owner>'
  - '&6成员 [<size>]: &e<members>'
  settings:
    player-limit: '&6玩家限制: &e<value>'
    open-party:
      enabled: '&6队伍现在开放'
      disabled: '&6点击开放您的队伍'
    public-party:
      enabled: '&6队伍现在公开并广播'
      disabled: '&6点击开放并广播您的队伍'
  # 其他队伍在物品栏中应该如何显示
  in-match-party: '&c<name> 的队伍（战斗中）'
  not-in-match-party: '&e<name> 的队伍（未战斗）'
  your-party: '&6您的队伍'
  # 允许用钓鱼竿击中队友
  allow-rod-boosting: false
  # /party fight 物品栏物品中的说明
  # 留空表示无成员列表
  member: '&e<name>'
  show-members-limit: 9
  # <more_members> 总是大于1，否则将使用'member:'
  show-members-limit-reached: '&e&l还有 <more_members> 个成员。'
  # 物品栏标题
  inventory-title: '&3队伍战斗'
  party-ffa-enabled: true
  # 开始队伍FFA的物品
  ffa-item: DIAMOND
  # 物品名称
  ffa-item-name: '&b队伍FFA'
  # 物品说明
  ffa-item-lore:
  - '&8与您队伍中的其他玩家战斗'
  # 物品栏中的槽位
  # 在0-53之间
  ffa-item-slot: 48
  # 队伍分割同样
  party-split-enabled: true
  split-item: DIAMOND_SWORD
  split-item-name: '&b分割队伍'
  split-item-lore:
  - '&8将您的队伍分成两个团队'
  - '&8与您队伍中的其他玩家战斗'
  split-item-slot: 49
  # 队伍回放同样
  party-playback-enabled: true
  party-playback-item: PAPER
  party-playback-item-name: '&b队伍回放'
  party-playback-item-lore:
  - '&8与您的队伍重播比赛'
  party-playback-item-slot: 50
  # 队伍对战机器人同样
  party-vs-bots-enabled: true
  party-vs-bots-item: SKULL_ITEM
  party-vs-bots-item-name: '&b队伍对战机器人'
  party-vs-bots-item-lore:
  - '&8与机器人战斗'
  party-vs-bots-item-slot: 51
  # 队伍聊天格式
  # <player> = 玩家名称
  # <message> = 消息
  chat-format: '&5<player>: &b<message>'
  # 如果玩家在消息前放置此内容，他们可以发送队伍消息
  # 例如 @让我们玩一些队伍对战机器人！
  player-chat-prefix: '@'
  # 创建队伍应该花费多少
  create:
    costs: false
    amount: 20
# 物品栏标题
party-settings-title: '&6队伍设置'
# 队伍中最大玩家数的默认值
max-party-members: 10
# 公共队伍消息应该多久发送一次（以秒为单位）
public-party-broadcast-time: 15
# 队伍邀请之间的冷却时间
# 以毫秒为单位
party-invite-cooldown: 3000
#                                             #
# ---------------------------------------------#
#  PvP事件 - 锦标赛/淘汰赛、山丘之王、         #
#  主宰者、相扑、LMS/最后一人站立              #
# ---------------------------------------------#
#                                             #
# strikepractice.cooldownbypass 将绕过这些冷却时间
# 以分钟为单位，每个用户
cooldowns:
  hostevent:
    brackets: 90
    sumo: 60
    lms: 90
    koth: 120
    juggernaut: 120
events-gui:
  title: '&c事件'
  size: 27
  items:
  - ==: GUIItem
    item:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: INK_SAC
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: '{"text":"","extra":[{"text":"主办相扑事件","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"green","bold":true}]}'
        lore:
        - '{"text":"","extra":[{"text":"点击主办相扑事件","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"gold","bold":false}]}'
        - '{"text":"","extra":[{"text":"冷却时间: <cooldown_sumo> [display=<is_cooldown_sumo>]","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"red","bold":false}]}'
        ItemFlags:
        - HIDE_ENCHANTS
        - HIDE_ATTRIBUTES
        - HIDE_UNBREAKABLE
        - HIDE_DESTROYS
        - HIDE_PLACED_ON
        - HIDE_POTION_EFFECTS
        - HIDE_DYE
        - HIDE_ARMOR_TRIM
    slot: 10
    command: /hostevent sumo
  - ==: GUIItem
    item:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: AIR
      amount: 0
    slot: 12
    command: /hostevent brackets
  - ==: GUIItem
    item:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: DIAMOND_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: '{"text":"","extra":[{"text":"主办最后一人站立事件","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"green","bold":true}]}'
        lore:
        - '{"text":"","extra":[{"text":"点击主办LMS事件","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"gold","bold":false}]}'
        - '{"text":"","extra":[{"text":"冷却时间: <cooldown_lms> [display=<is_cooldown_lms>]","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"red","bold":false}]}'
        ItemFlags:
        - HIDE_ENCHANTS
        - HIDE_ATTRIBUTES
        - HIDE_UNBREAKABLE
        - HIDE_DESTROYS
        - HIDE_PLACED_ON
        - HIDE_POTION_EFFECTS
        - HIDE_DYE
        - HIDE_ARMOR_TRIM
    slot: 14
    command: /hostevent lms
  - ==: GUIItem
    item:
      ==: org.bukkit.inventory.ItemStack
      v: 3700
      type: GOLDEN_SWORD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: '{"text":"","extra":[{"text":"主办山丘之王事件","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"green","bold":true}]}'
        lore:
        - '{"text":"","extra":[{"text":"点击主办KOTH事件","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"gold","bold":false}]}'
        - '{"text":"","extra":[{"text":"冷却时间: <cooldown_koth> [display=<is_cooldown_koth>]","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"red","bold":false}]}'
        ItemFlags:
        - HIDE_ENCHANTS
        - HIDE_ATTRIBUTES
        - HIDE_UNBREAKABLE
        - HIDE_DESTROYS
        - HIDE_PLACED_ON
        - HIDE_POTION_EFFECTS
        - HIDE_DYE
        - HIDE_ARMOR_TRIM
    slot: 16
    command: /hostevent koth
automatic-events:
  broadcast:
    # 消息应该多久发送一次
    delay: 10
    # 消息应该发送多少次
    times: 5
    # 所有事件的消息
    lms: '&c&l<host> 正在主办 <kit>&c&l LMS事件！点击加入！'
    brackets: '&c&l<host> 正在主办 <kit>&c&l 1v1锦标赛事件！点击加入！'
    sumo: '&c&l<host> 正在主办相扑事件！点击加入！'
    juggernaut: '&c&l<host> 正在主办主宰者事件！点击加入！'
    koth: '&c&l<host> 正在主办KOTH事件！点击加入！'
  # 自动主办时的默认套装
  juggernaut-kit: gapple
  juggernaut-player-kit: nodebuff
  koth-kit: nodebuff
  lms-kit: nodebuff
  brackets-kit: nodebuff
  sumo-kit: sumo
  times:
  - 25:30:lms
brackets:
  allow-spectating: true
  # 当玩家赢得事件时将执行的命令
  # <player> 将被替换为玩家的名称
  winner-cmd: eco give <player> 100
  # 当玩家数量达到时自动开始事件
  auto-start: 15
  # 多少秒后应该自动开始事件
  auto-start-after-seconds: 10
sumo:
  allow-spectating: true
  # 当玩家赢得事件时将执行的命令
  # <player> 将被替换为玩家的名称
  winner-cmd: eco give <player> 100
  # 当玩家数量达到时自动开始事件
  auto-start: 15
  # 多少秒后应该自动开始事件
  auto-start-after-seconds: 10
koth:
  # 玩家是否应该能够随时加入或重新加入事件
  anytime-join: true
  respawn: true
  auto-respawn: true
  # 队伍名称
  team1: '&a&l绿队'
  team2: '&c&l红队'
  # 占领时间应该是多少秒
  timer: 300
# 客户端上的末影珍珠冷却
client-cooldowns:
  # Lunar客户端
  lunar: true
  # 下载BadlionClientTimerAPI以支持BAC的冷却时间
  # https://github.com/BadlionNetwork/BadlionClientTimerAPI/releases
  badlion: true
# 请勿更改
version: 3.12.0-SNAPSHOT
lobby: -4.5, 65.0, 0.5, -90.30001, 0.59998167, Hub
editing-place: 16.684448079939127, 67.0, -39.86250001192093, 177.3058, 0.1497686,
  Hub
